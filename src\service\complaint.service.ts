import { ILogger, Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Op, fn } from 'sequelize';
import { Complaint, Customer, Order, Employee } from '../entity';
import { BaseService } from '../common/BaseService';
import { CustomError } from '../error/custom.error';
import { ComplaintStatus } from '../common/Constant';

@Provide()
export class ComplaintService extends BaseService<Complaint> {
  @Inject()
  ctx: Context;

  @Inject()
  logger: ILogger;

  constructor() {
    super('投诉建议');
  }

  getModel() {
    return Complaint;
  }

  /**
   * 创建投诉建议
   * @param customerId 客户ID
   * @param complaintData 投诉建议数据
   */
  async createComplaint(
    customerId: number,
    complaintData: {
      category: 'complaint' | 'suggestion';
      subCategory: 'order' | 'employee' | 'platform' | 'service';
      title: string;
      content: string;
      orderId?: number;
      employeeId?: number;
      contactInfo?: string;
      photoURLs?: string[];
    }
  ) {
    const {
      category,
      subCategory,
      title,
      content,
      orderId,
      employeeId,
      contactInfo,
      photoURLs,
    } = complaintData;

    // 验证客户是否存在
    const customer = await Customer.findByPk(customerId);
    if (!customer) {
      throw new CustomError('客户不存在', 404);
    }

    // 如果是订单投诉，验证订单是否存在且属于该客户，并获取服务人员信息
    let orderEmployeeId = employeeId;
    if (subCategory === 'order' && orderId) {
      const order = await Order.findOne({
        where: { id: orderId, customerId },
        attributes: ['id', 'customerId', 'employeeId'],
      });
      if (!order) {
        throw new CustomError('订单不存在或不属于该客户', 404);
      }

      // 如果订单有服务人员信息，自动设置员工ID
      if (order.employeeId) {
        orderEmployeeId = order.employeeId;
        this.logger.info(
          `订单投诉自动关联服务人员: orderId=${orderId}, employeeId=${order.employeeId}`
        );
      }
    }

    // 如果是人员投诉，验证员工是否存在
    if (subCategory === 'employee' && employeeId) {
      const employee = await Employee.findByPk(employeeId);
      if (!employee) {
        throw new CustomError('员工不存在', 404);
      }
    }

    // 创建投诉建议
    const complaint = await Complaint.create({
      customerId,
      category,
      subCategory,
      title,
      content,
      orderId,
      employeeId: orderEmployeeId, // 使用从订单获取的员工ID或原始传入的员工ID
      contactInfo,
      photoURLs: photoURLs || [],
      status: ComplaintStatus.PENDING,
    });

    // 返回包含关联信息的完整数据
    return await this.getComplaintWithRelations(complaint.id);
  }

  /**
   * 获取包含关联信息的投诉建议
   * @param complaintId 投诉建议ID
   */
  async getComplaintWithRelations(complaintId: number) {
    return await Complaint.findByPk(complaintId, {
      include: [
        {
          model: Customer,
          attributes: ['id', 'nickname', 'phone', 'avatar'],
        },
        {
          model: Order,
          attributes: ['id', 'sn', 'status', 'serviceTime'],
        },
        {
          model: Employee,
          attributes: ['id', 'name', 'phone', 'avatar'],
        },
      ],
    });
  }

  /**
   * 更新投诉建议
   * @param customerId 客户ID
   * @param complaintId 投诉建议ID
   * @param updateData 更新数据
   */
  async updateComplaint(
    customerId: number,
    complaintId: number,
    updateData: {
      category?: 'complaint' | 'suggestion';
      subCategory?: 'order' | 'employee' | 'platform' | 'service';
      title?: string;
      content?: string;
      orderId?: number;
      employeeId?: number;
      contactInfo?: string;
      photoURLs?: string[];
    }
  ) {
    const complaint = await Complaint.findOne({
      where: { id: complaintId, customerId },
    });

    if (!complaint) {
      throw new CustomError('投诉建议不存在或不属于该客户', 404);
    }

    // 只有待处理状态的投诉建议才能修改
    if (complaint.status !== ComplaintStatus.PENDING) {
      throw new CustomError('只有待处理状态的投诉建议才能修改', 400);
    }

    // 验证关联数据并处理订单投诉的员工ID自动设置
    const finalUpdateData = { ...updateData };

    if (updateData.orderId && updateData.subCategory === 'order') {
      const order = await Order.findOne({
        where: { id: updateData.orderId, customerId },
        attributes: ['id', 'customerId', 'employeeId'],
      });
      if (!order) {
        throw new CustomError('订单不存在或不属于该客户', 404);
      }

      // 如果订单有服务人员信息，自动设置员工ID
      if (order.employeeId) {
        finalUpdateData.employeeId = order.employeeId;
        this.logger.info(
          `更新订单投诉自动关联服务人员: orderId=${updateData.orderId}, employeeId=${order.employeeId}`
        );
      }
    }

    if (updateData.employeeId && updateData.subCategory === 'employee') {
      const employee = await Employee.findByPk(updateData.employeeId);
      if (!employee) {
        throw new CustomError('员工不存在', 404);
      }
    }

    await complaint.update(finalUpdateData);

    return await this.getComplaintWithRelations(complaintId);
  }

  /**
   * 删除投诉建议
   * @param complaintId 投诉建议ID
   */
  async deleteComplaint(complaintId: number) {
    const complaint = await Complaint.findOne({
      where: { id: complaintId },
    });

    if (!complaint) {
      throw new CustomError('投诉建议不存在或不属于该客户', 404);
    }

    // 只有待处理状态的投诉建议才能删除
    if (complaint.status !== ComplaintStatus.PENDING) {
      throw new CustomError('只有待处理状态的投诉建议才能删除', 400);
    }

    await complaint.destroy();
    return true;
  }

  /**
   * 处理投诉建议
   * @param complaintId 投诉建议ID
   * @param handleData 处理数据
   */
  async handleComplaint(
    complaintId: number,
    handleData: {
      status: 'processing' | 'resolved' | 'closed';
      result: string;
      handlerId: number;
    }
  ) {
    const complaint = await Complaint.findByPk(complaintId);

    if (!complaint) {
      throw new CustomError('投诉建议不存在', 404);
    }

    // 验证处理人员是否存在（这里假设handlerId是管理员ID，实际可能需要验证管理员表）
    // const handler = await Admin.findByPk(handleData.handlerId);
    // if (!handler) {
    //   throw new CustomError('处理人员不存在', 404);
    // }

    await complaint.update({
      status: handleData.status,
      result: handleData.result,
      handlerId: handleData.handlerId,
      handledAt: new Date(),
    });

    return await this.getComplaintWithRelations(complaintId);
  }

  /**
   * 获取客户的投诉建议列表
   * @param customerId 客户ID
   * @param page 页码
   * @param pageSize 每页数量
   */
  async getCustomerComplaints(customerId: number, page = 1, pageSize = 10) {
    const offset = (page - 1) * pageSize;

    const result = await this.findAll({
      query: { customerId },
      offset,
      limit: pageSize,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: Order,
          attributes: ['id', 'sn', 'status', 'serviceTime'],
        },
        {
          model: Employee,
          attributes: ['id', 'name', 'phone'],
        },
      ],
    });

    return {
      ...result,
      page,
      pageSize,
      totalPages: Math.ceil(result.total / pageSize),
    };
  }

  /**
   * 获取投诉建议统计信息
   * @param filters 过滤条件
   */
  async getComplaintStatistics(
    filters: {
      startDate?: string;
      endDate?: string;
      category?: 'complaint' | 'suggestion';
      subCategory?: 'order' | 'employee' | 'platform' | 'service';
    } = {}
  ) {
    const whereCondition: any = {};

    // 处理日期范围
    if (filters.startDate || filters.endDate) {
      whereCondition.createdAt = {};
      if (filters.startDate) {
        whereCondition.createdAt[Op.gte] = new Date(filters.startDate);
      }
      if (filters.endDate) {
        whereCondition.createdAt[Op.lte] = new Date(
          filters.endDate + ' 23:59:59'
        );
      }
    }

    // 处理类型过滤
    if (filters.category) {
      whereCondition.category = filters.category;
    }
    if (filters.subCategory) {
      whereCondition.subCategory = filters.subCategory;
    }

    // 总数统计
    const total = await Complaint.count({ where: whereCondition });

    // 按状态统计
    const statusStats = await Complaint.findAll({
      where: whereCondition,
      attributes: ['status', [fn('COUNT', '*'), 'count']],
      group: ['status'],
      raw: true,
    });

    // 按类型统计
    const categoryStats = await Complaint.findAll({
      where: whereCondition,
      attributes: ['category', [fn('COUNT', '*'), 'count']],
      group: ['category'],
      raw: true,
    });

    // 按子类型统计
    const subCategoryStats = await Complaint.findAll({
      where: whereCondition,
      attributes: ['subCategory', [fn('COUNT', '*'), 'count']],
      group: ['subCategory'],
      raw: true,
    });

    return {
      total,
      statusStats,
      categoryStats,
      subCategoryStats,
    };
  }
}
